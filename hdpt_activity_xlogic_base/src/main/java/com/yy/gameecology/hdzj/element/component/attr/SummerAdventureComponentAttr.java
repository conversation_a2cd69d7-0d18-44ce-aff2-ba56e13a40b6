package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.ComponentAttrField;
import lombok.Data;

/**
 * 夏日探险组件属性配置
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", remark = "交友500 聊天室810")
    private long busiId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp日榜单id")
    private long cpDailyRankId;

    @ComponentAttrField(labelText = "cp 用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp 主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "厅角色id")
    private String tingRoleId;

    @ComponentAttrField(labelText = "获得骰子所需礼物金额", remark = "单位：分，131.4元=13140分")
    private long diceGiftAmount = 13140;

    @ComponentAttrField(labelText = "每次探险最多使用骰子数量")
    private int maxDicePerAdventure = 30;

    @ComponentAttrField(labelText = "CP活动期间最多可获得骰子数量")
    private int maxDiceTotal = 500;

    @ComponentAttrField(labelText = "地图格子总数")
    private int mapGridCount = 30;

    @ComponentAttrField(labelText = "奖励任务ID")
    private long rewardTaskId;

    @ComponentAttrField(labelText = "用户奖励包ID")
    private long userRewardPackageId;

    @ComponentAttrField(labelText = "主持奖励包ID")
    private long anchorRewardPackageId;

    @ComponentAttrField(labelText = "是否开启实时统计推送")
    private boolean enableRealtimeStats = true;

    @ComponentAttrField(labelText = "统计推送群ID")
    private String statsGroupId;

    @ComponentAttrField(labelText = "榜单拉取数量")
    private long rankLimit = 100;

    @ComponentAttrField(labelText = "分值阈值")
    private long scoreThreshold = 1000;
}
