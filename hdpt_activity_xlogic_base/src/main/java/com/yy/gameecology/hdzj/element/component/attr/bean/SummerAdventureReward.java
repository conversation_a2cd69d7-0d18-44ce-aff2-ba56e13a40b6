package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;

/**
 * 夏日探险奖励配置
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureReward {
    
    /**
     * 活动ID
     */
    private long actId;
    
    /**
     * 组件使用索引
     */
    private long cmptUseInx;
    
    /**
     * 地图格子位置 (1-30)
     */
    private int gridPosition;
    
    /**
     * 奖励类型 (1:头像框 2:气泡 3:礼物 4:其他)
     */
    private int rewardType;
    
    /**
     * 奖励ID
     */
    private String rewardId;
    
    /**
     * 奖励名称
     */
    private String rewardName;
    
    /**
     * 奖励描述
     */
    private String rewardDesc;
    
    /**
     * 奖励图标
     */
    private String rewardIcon;
    
    /**
     * 奖励数量
     */
    private int rewardCount;
    
    /**
     * 奖励库存 (-1表示无限制)
     */
    private int rewardStock;
    
    /**
     * 已分配数量
     */
    private int allocatedCount;
    
    /**
     * 权重 (用于随机分配)
     */
    private int weight;
    
    /**
     * 是否启用
     */
    private boolean enabled;
    
    /**
     * 创建时间
     */
    private long createTime;
}
