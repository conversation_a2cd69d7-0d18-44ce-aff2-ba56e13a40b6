package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;
import java.util.List;

/**
 * 夏日探险记录
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureRecord {
    
    /**
     * 记录ID
     */
    private String recordId;
    
    /**
     * 活动ID
     */
    private long actId;
    
    /**
     * 组件使用索引
     */
    private long cmptUseInx;
    
    /**
     * 用户UID
     */
    private long uid;
    
    /**
     * 主持UID
     */
    private long anchorUid;
    
    /**
     * CP成员标识
     */
    private String cpMember;
    
    /**
     * 使用的骰子数量
     */
    private int usedDiceCount;
    
    /**
     * 探险路径 (JSON格式存储每步的位置和奖励)
     */
    private String adventurePath;
    
    /**
     * 获得的奖励列表 (JSON格式)
     */
    private String rewardList;
    
    /**
     * 探险开始时间
     */
    private long startTime;
    
    /**
     * 探险结束时间
     */
    private long endTime;
    
    /**
     * 创建时间
     */
    private long createTime;
}
